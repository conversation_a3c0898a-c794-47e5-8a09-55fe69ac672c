import { ConflictException, Injectable } from '@nestjs/common';
import * as sgMail from '@sendgrid/mail';

@Injectable()
export class SendgridService {
  constructor() {
    // Initialize SendGrid with the API key
    sgMail.setApiKey(process.env.SENDGRID_API_KEY);
  }

  async sendEmail(to: string, subject: string, content: string) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const msg = {
      to,
      from: process.env.SENDGRID_FROM_EMAIL, // Use your verified SendGrid sender email
      subject,
      text: content,
      html: `<p>${content}</p>`,
    };

    try {
      // console.log(msg);
      await sgMail.send(msg);
      console.log('Email sent successfully');
    } catch (error) {
      console.error(
        'Error sending email:',
        error?.response?.body.errors[0].message,
      );
      throw new ConflictException(error?.response?.body.errors[0].message);
    }
  }
}
