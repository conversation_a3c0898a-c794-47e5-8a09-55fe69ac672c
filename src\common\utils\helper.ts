import { Constants } from '../constants';
import { ConfigService } from '@nestjs/config';

export const extractTokenFromHeader = (
  request: Request,
): string | undefined => {
  const [type, token] = request.headers['authorization']?.split(' ') ?? [];
  return type === 'Bearer' ? token : undefined;
};

export const calculateValidTillDate = (packageType: string): Date => {
  const date = new Date();
  switch (packageType) {
    case 'yearly':
      // Add 1 year to the current date
      date.setFullYear(date.getFullYear() + 1);
      break;
    case 'monthly':
      // Add 1 month to the current date
      date.setMonth(date.getMonth() + 1);
      break;
    case 'quarterly':
      // Add 3 months to the current date
      date.setMonth(date.getMonth() + 3);
      break;
    case 'default':
      // Add 5 years to the current date
      date.setFullYear(date.getFullYear() + 5);
      break;
    default:
      throw new Error('Invalid package type');
  }
  return date;
};
// const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
export const generateOTP = (value: string) => {
  // const configService = new ConfigService();
  console.log(value);

  // if (
  //   configService.get('NODE_ENV') != null &&
  //   [Constants.NodeEnv.QA].includes(configService.get('NODE_ENV') ?? '')
  // ) {
  //   if (value) {
  //     return Math.floor(100000 + Math.random() * 900000).toString();
  //   } else {
  //     return '123456';
  //   }
  // } else {
  return '123456';
  // }
};
