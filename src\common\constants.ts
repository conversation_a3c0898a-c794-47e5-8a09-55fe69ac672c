const Roles = {
  SUPER_ADMIN: 'super_admin',
  ADMIN: 'admin',
  BUYER: 'buyer',
  SELLER: 'seller',
  CONTRACTOR: 'contractor',
  LABOUR: 'labour',
  ARCHITECT: 'architect',
};

const Status = {
  ACTIVE: 'Active',
  INACTIVE: 'Inactive',
  DELETE: 'Delete',
  DRAFT: 'Draft',
  IN_REVIEW: 'In_review',
  APPROVED: 'Approved',
  REJECTED: 'Rejected',
  PUBLISHED: 'Published',
};

const DeletionRequestStatus = {
  PENDING: 'pending',
  APPROVED: 'approved',
  REJECTED: 'rejected',
};

const Titles = {
  WelcomeToApnaBuilder: 'Welcome to Apna Builder',
  CompleteYourProfile: 'Complete Your Profile',
  VerificationOTP: 'Verification OTP',
  SubscriptionActivated: 'Subscription Activated',
  SubscriptionRenewalDue: 'Subscription Renewal Due',
  PostNewJob: 'Post a New Job',
  NewBidRequest: 'New Bid Requested',
  NewQuotationRequest: 'New Quotation Requested',
  NewJobAlert: 'New Job Alert',
};
const NodeEnv = {
  DEVELOPMENT: 'development',
  QA: 'qa',
  UAT: 'uat',
};

export const Constants = {
  Roles,
  Status,
  DeletionRequestStatus,
  Titles,
  NodeEnv,
};

export const rolePermissions = {
  default: {
    buyer: {
      radiusLimit: 2,
      rfqRequestLimit: 0,
      jobPostingLimit: 0,
      connectionRequestLimit: 0,
      bidSubmissionLimit: 0,
      submitReviewLimit: 0,
      productListLimit: 0,
    },
    architect: {
      radiusLimit: 2,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      jobPostingLimit: 0,
      connectionRequestLimit: 0,
      submitReviewLimit: 0,
      productListLimit: 0,
    },
    contractor: {
      radiusLimit: 2,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      jobPostingLimit: 0,
      connectionRequestLimit: 0,
      submitReviewLimit: 0,
      productListLimit: 0,
    },
    seller: {
      radiusLimit: 2,
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      submitReviewLimit: 0,
      connectionRequestLimit: 0,
      productListLimit: 0,
    },
    labour: {
      radiusLimit: 2,
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      connectionRequestLimit: 0,
      submitReviewLimit: 0,
      productListLimit: 0,
    },
  },
  free: {
    buyer: {
      radiusLimit: 10,
      rfqRequestLimit: 1,
      jobPostingLimit: 2,
      connectionRequestLimit: 5,
      bidSubmissionLimit: 0,
      submitReviewLimit: 2,
      productListLimit: 0,
    },
    architect: {
      radiusLimit: 10,
      rfqRequestLimit: 1,
      bidSubmissionLimit: 3,
      jobPostingLimit: 2,
      connectionRequestLimit: 5,
      submitReviewLimit: 2,
      productListLimit: 0,
    },
    contractor: {
      radiusLimit: 10,
      rfqRequestLimit: 1,
      bidSubmissionLimit: 3,
      jobPostingLimit: 2,
      connectionRequestLimit: 5,
      submitReviewLimit: 2,
      productListLimit: 0,
    },
    seller: {
      radiusLimit: 10,
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      submitReviewLimit: 2,
      connectionRequestLimit: 0,
      productListLimit: 1,
    },
    labour: {
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 3,
      radiusLimit: 10,
      connectionRequestLimit: 0,
      submitReviewLimit: 0,
      productListLimit: 0,
    },
  },
  basic: {
    buyer: {
      radiusLimit: 80,
      rfqRequestLimit: 30,
      jobPostingLimit: 15,
      bidSubmissionLimit: 0,
      connectionRequestLimit: 20,
      submitReviewLimit: 10,
      productListLimit: 0,
    },
    architect: {
      radiusLimit: 80,
      rfqRequestLimit: 30,
      bidSubmissionLimit: 30,
      jobPostingLimit: 15,
      connectionRequestLimit: 0,
      submitReviewLimit: 10,
      productListLimit: 0,
    },
    contractor: {
      radiusLimit: 80,
      rfqRequestLimit: 30,
      bidSubmissionLimit: 30,
      jobPostingLimit: 15,
      connectionRequestLimit: 0,
      submitReviewLimit: 10,
      productListLimit: 0,
    },
    seller: {
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      radiusLimit: 10,
      connectionRequestLimit: 20,
      submitReviewLimit: 10,
      productListLimit: 5,
    },
    labour: {
      radiusLimit: 80,
      bidSubmissionLimit: 30,
      rfqRequestLimit: 0,
      jobPostingLimit: 0,
      connectionRequestLimit: 20,
      submitReviewLimit: 10,
      productListLimit: 0,
    },
  },
  premium: {
    seller: {
      jobPostingLimit: 0,
      rfqRequestLimit: 0,
      bidSubmissionLimit: 0,
      radiusLimit: 80,
      connectionRequestLimit: 40,
      submitReviewLimit: 20,
      productListLimit: 10,
    },
  },
};

export const TEMPLATE_IDS = {
  Welcome_Sms: '67209f2ed6fc055f6b2e0a02',
  Otp_Sms: '67612f92d6fc0532b97d33f3',
  Subscription_Renewal_Due: '6720a07cd6fc0558cd0fbd02',
  Payment_successful: '676e9bf0d6fc050637036575',
};
