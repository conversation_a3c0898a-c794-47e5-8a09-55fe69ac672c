/* eslint-disable @typescript-eslint/no-unused-vars */
import {
  Controller,
  Get,
  Post,
  Body,
  Query,
  UseGuards,
  Request,
  Param,
  Patch,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiQuery,
} from '@nestjs/swagger';

import { ReviewRatingService } from './review-rating.service';
import { JWTGuard } from 'src/guards/common/jwt.guard';
import {
  CreateReplyDto,
  CreateReviewRatingDto,
  InviteForReviewRatingDto,
} from './dto/review-rating.dto';
import { ReviewRating } from './schemas/review-rating.schema';
import { ReviewRatingStatus } from './utils/enum';
import { User } from '../admin/authentication/schemas/user.schema';
import { Role } from '../roles/schemas/role.schema';
import { InjectModel } from '@nestjs/sequelize';
import { SendgridService } from '../sendgrid/sendgrid.service';

@ApiTags('Review Ratings')
@UseGuards(JWTGuard)
@ApiBearerAuth()
@Controller('review-ratings')
export class ReviewRatingController {
  constructor(
    private readonly reviewRatingService: ReviewRatingService,
    @InjectModel(User)
    private userModel: typeof User,
    private readonly sendgridService: SendgridService,
  ) {}

  @Post()
  @ApiOperation({ summary: 'Create a  review rating' })
  @ApiResponse({
    status: 201,
    description: 'Review rating created successfully',
  })
  async create(
    @Body() createReviewRatingDto: CreateReviewRatingDto,
    @Request() req,
  ): Promise<ReviewRating> {
    const payload = {
      ...createReviewRatingDto,
      reviewerId: req.user.id,
      status: ReviewRatingStatus.ACTIVE,
    };
    // console.log('payload....', payload);
    return this.reviewRatingService.create(payload);
  }

  @Post('request')
  @UseGuards(JWTGuard)
  @ApiOperation({ summary: 'Create a request for  review rating' })
  @ApiResponse({
    status: 201,
    description: 'Review rating created successfully',
  })
  async inviteForReview(
    @Body() inviteForReviewRatingDto: InviteForReviewRatingDto,
    @Request() req,
  ): Promise<{ message: string }> {
    const result = await this.reviewRatingService.inviteForReviewRating(
      inviteForReviewRatingDto,
      req.user,
    );
    return result;
  }

  @Get()
  @ApiOperation({ summary: 'Get all review ratings' })
  @ApiResponse({ status: 200, description: 'Return all review ratings' })
  @ApiQuery({ name: 'limit', required: false, type: String })
  @ApiQuery({ name: 'page', required: false, type: String })
  @ApiQuery({ name: 'isVerified', required: false, type: Boolean })
  @ApiQuery({
    name: 'userId',
    required: true,
    type: Number,
    description: 'ID of the user to fetch review ratings for',
  })
  @ApiQuery({
    name: 'sort',
    required: true,
    type: String,
    enum: ['ASC', 'DESC'],
  })
  async findAll(
    @Request() req,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('userId') userId: number,
    @Query('isVerified') isVerified: boolean = true,
    @Query('sort') sort: string,
  ): Promise<{
    reviewRatings: ReviewRating[];
    total: number;
  }> {
    const sortOrder = sort?.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';
    const offset = (page - 1) * limit;

    const where: any = {
      revieweeId: userId,
      parentId: null,
    };

    const query = {
      where,
      limit,
      offset,
      include: [
        {
          model: User,
          as: 'reviewer',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
          include: [
            {
              model: Role,
              through: { attributes: [] },
            },
          ],
        },
        {
          model: User,
          as: 'reviewee',
          attributes: [
            'id',
            'firstName',
            'lastName',
            'email',
            'profilePicture',
          ],
          include: [
            {
              model: Role,
              through: { attributes: [] },
            },
          ],
        },
        { model: ReviewRating, as: 'reply', attributes: ['id', 'review'] },
      ],
      order: [['rating', sortOrder]],
    };
    return this.reviewRatingService.findAndCountAll(query);
  }

  @Post(':id/reply')
  @ApiOperation({ summary: 'Create a reply to a review' })
  @ApiResponse({
    status: 201,
    description: 'Reply created successfully',
    type: ReviewRating,
  })
  @ApiResponse({ status: 400, description: 'Bad request' })
  async createReply(
    @Request() req,
    @Param('id') id: number,
    @Body() createReplyDto: CreateReplyDto,
  ): Promise<ReviewRating> {
    return this.reviewRatingService.createReply(id, req.user, createReplyDto);
  }

  @Patch(':id/verify')
  @ApiOperation({ summary: 'Verify a review rating' })
  @ApiResponse({
    status: 200,
    description: 'Review rating verified successfully',
  })
  async update(@Param('id') id: number, @Request() req): Promise<ReviewRating> {
    return this.reviewRatingService.verifyReview(id, req.user.id);
  }
}
