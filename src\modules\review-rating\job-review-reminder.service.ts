/* eslint-disable @typescript-eslint/no-unused-vars */
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { <PERSON>ron } from '@nestjs/schedule';
import { Op } from 'sequelize';

import { Job } from '../job/schemas/job.schema';
import { User } from '../admin/authentication/schemas/user.schema';
import { ReviewRating } from './schemas/review-rating.schema';
import { ReviewReminder } from './schemas/review-reminder.schema';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { NotificationService } from '../notification/notification.service';
import { Email } from 'src/common/email-template';
import { JobStatus } from 'src/common/utils/enum';
import { notificationType } from 'src/common/messages';

@Injectable()
export class JobReviewReminderService {
  constructor(
    @InjectModel(Job)
    private jobModel: typeof Job,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(ReviewRating)
    private reviewRatingModel: typeof ReviewRating,
    @InjectModel(ReviewReminder)
    private reviewReminderModel: typeof ReviewReminder,
    private readonly sendgridService: SendgridService,
    private readonly notificationService: NotificationService,
  ) {}

  /**
   * Find jobs that were completed at least 24 hours ago
   */
  private async findJobsCompletedAfter24Hours() {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const twoDaysAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);

    console.log(
      `[24h] Looking for jobs closed between ${twoDaysAgo.toISOString()} and ${oneDayAgo.toISOString()}`,
    );

    const jobs = await this.jobModel.findAll({
      where: {
        status: JobStatus.CLOSED,
        updatedAt: {
          [Op.between]: [twoDaysAgo, oneDayAgo], // Jobs closed between 24-48 hours ago
        },
      },
      include: [
        {
          model: User,
          as: 'createdBy',
          required: false,
          attributes: ['id', 'firstName', 'lastName', 'email'],
          include: [
            {
              model: NotificationPreference,
              as: 'notificationPreference',
              required: false,
            },
          ],
        },
        {
          model: ReviewRating,
          as: 'reviewRating',
          required: false,
          where: {
            status: 'active',
          },
        },
      ],
    });

    console.log(`[24h] Found ${jobs.length} jobs matching criteria`);
    return jobs;
  }

  /**
   * Find jobs that were completed at least 48 hours ago
   */
  private async findJobsCompletedAfter48Hours() {
    const twoDaysAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);

    console.log(
      `[48h] Looking for jobs closed before ${twoDaysAgo.toISOString()}`,
    );

    const jobs = await this.jobModel.findAll({
      where: {
        status: JobStatus.CLOSED,
        updatedAt: {
          [Op.lt]: twoDaysAgo, // Jobs closed at least 48 hours ago
        },
      },
      include: [
        {
          model: User,
          as: 'createdBy',
          required: false,
          attributes: ['id', 'firstName', 'lastName', 'email'],
          include: [
            {
              model: NotificationPreference,
              as: 'notificationPreference',
              required: false,
            },
          ],
        },
        {
          model: ReviewRating,
          as: 'reviewRating',
          required: false,
          where: {
            status: 'active',
          },
        },
      ],
    });

    console.log(`[48h] Found ${jobs.length} jobs matching criteria`);
    return jobs;
  }

  /**
   * Check if a user has submitted a review for a job
   */
  private hasUserSubmittedReview(job: any, userId: number): boolean {
    return (
      Array.isArray(job.reviewRating) &&
      job.reviewRating.some(
        (review: any) =>
          review.reviewerId === userId &&
          review.jobId === job.id &&
          review.status === 'active',
      )
    );
  }

  /**
   * Send 24-hour reminder email
   */
  private async send24HourEmailReminder(
    user: any,
    jobTitle: string,
  ): Promise<void> {
    console.log(
      `[24h Email] Sending email to ${user.email} for job: ${jobTitle}`,
    );

    try {
      await this.sendgridService.sendEmail(
        user.email,
        'How was your experience?',
        Email.getTemplate({
          title: 'How was your experience?',
          userName: `${user?.firstName} ${user?.lastName}`,
          children: `
            <p>
              Please take a moment to review your experience with the job "${jobTitle}".
              Your feedback helps our community!
            </p>
          `,
        }),
      );
      console.log(`[24h Email] Successfully sent email to ${user.email}`);
    } catch (error) {
      console.error(
        `[24h Email] Failed to send email to ${user.email}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Send 48-hour reminder email
   */
  private async send48HourEmailReminder(
    user: any,
    jobTitle: string,
  ): Promise<void> {
    console.log(
      `[48h Email] Sending email to ${user.email} for job: ${jobTitle}`,
    );

    try {
      await this.sendgridService.sendEmail(
        user.email,
        "Don't Forget to Review!",
        Email.getTemplate({
          title: "Don't Forget to Review!",
          userName: `${user?.firstName} ${user?.lastName}`,
          children: `
            <p>
              Please take a moment to review your experience with the job "${jobTitle}".
              Your feedback helps our community!
            </p>
          `,
        }),
      );
      console.log(`[48h Email] Successfully sent email to ${user.email}`);
    } catch (error) {
      console.error(
        `[48h Email] Failed to send email to ${user.email}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Send in-app notification reminder
   */
  private async sendInAppReminder(
    userId: number,
    jobId: number,
    reminderType: '24h' | '48h',
    jobTitle: string,
  ): Promise<void> {
    console.log(
      `[${reminderType} Notification] Sending in-app notification to user ${userId} for job ${jobId}`,
    );

    try {
      const message =
        reminderType === '24h'
          ? `How was your experience with "${jobTitle}"? Please leave a review to help our community!`
          : `Don't forget to review your experience with "${jobTitle}". Your feedback is valuable!`;

      const data = {
        type: notificationType.REVIEW_REMINDER,
        receiverId: userId,
        prefrence: 'inAppNotifications',
        message: message,
        additional: {
          jobId: jobId,
          reminderType: reminderType,
          jobTitle: jobTitle,
        },
      };
      await this.notificationService.sendNotification(data);
      console.log(
        `[${reminderType} Notification] Successfully sent in-app notification to user ${userId}`,
      );
    } catch (error) {
      console.error(
        `[${reminderType} Notification] Failed to send notification to user ${userId}:`,
        error.message,
      );
      throw error;
    }
  }

  /**
   * Send 24-hour reminder to a user
   */
  private async send24HourReminder(job: any, user: any): Promise<void> {
    console.log(
      `[24h Reminder] Processing reminder for user ${user.id}, job ${job.id} (${job.title})`,
    );

    // Check if user has already submitted a review
    const existingReview = await this.reviewRatingModel.findOne({
      where: {
        reviewerId: user.id,
        jobId: job.id,
        status: 'active',
      },
    });

    if (existingReview) {
      console.log(
        `[24h Reminder] Review already exists for user ${user.id} and job ${job.id}, skipping reminder`,
      );
      return;
    }

    console.log(
      `[24h Reminder] No existing review found, proceeding with reminder`,
    );

    // Check if we've already sent the 24-hour reminder
    const existingReminder = await this.reviewReminderModel.findOne({
      where: {
        jobId: job.id,
        userId: user.id,
      },
    });

    if (existingReminder) {
      console.log(
        `[24h Reminder] Found existing reminder record for user ${user.id}, job ${job.id}`,
      );
      console.log(
        `[24h Reminder] EmailSent: ${existingReminder.emailSent}, NotificationSent: ${existingReminder.notificationSent}`,
      );
    } else {
      console.log(
        `[24h Reminder] No existing reminder record found, creating new one`,
      );
    }

    // Create reminder record if it doesn't exist
    const reminderRecord =
      existingReminder ||
      (await this.reviewReminderModel.create({
        jobId: job.id,
        userId: user.id,
        emailSent: false,
        notificationSent: false,
      }));

    // Send 24-hour email reminder if not sent already
    if (user.email && !reminderRecord.email24hSent) {
      await this.send24HourEmailReminder(user, job.title);
      reminderRecord.email24hSent = true;
      await reminderRecord.save();
      console.log(
        `24h email reminder sent to user ${user.id} for job ${job.id}`,
      );
    }

    // Send 24-hour in-app notification if not sent already
    if (
      user.notificationPreference?.inAppNotifications &&
      !reminderRecord.notification24hSent
    ) {
      await this.sendInAppReminder(user.id, job.id, '24h');
      reminderRecord.notification24hSent = true;
      await reminderRecord.save();
      console.log(
        `24h in-app reminder sent to user ${user.id} for job ${job.id}`,
      );
    }
  }

  /**
   * Send 48-hour reminder to a user
   */
  private async send48HourReminder(job: any, user: any): Promise<void> {
    console.log(
      `[48h Reminder] Processing reminder for user ${user.id}, job ${job.id} (${job.title})`,
    );

    // Check if user has already submitted a review
    const existingReview = await this.reviewRatingModel.findOne({
      where: {
        reviewerId: user.id,
        jobId: job.id,
        status: 'active',
      },
    });

    if (existingReview) {
      console.log(
        `[48h Reminder] Review already exists for user ${user.id} and job ${job.id}, skipping reminder`,
      );
      return;
    }

    console.log(
      `[48h Reminder] No existing review found, proceeding with reminder`,
    );

    // Check existing reminder record
    const existingReminder = await this.reviewReminderModel.findOne({
      where: {
        jobId: job.id,
        userId: user.id,
      },
    });

    // Create reminder record if it doesn't exist
    const reminderRecord =
      existingReminder ||
      (await this.reviewReminderModel.create({
        jobId: job.id,
        userId: user.id,
        emailSent: false,
        notificationSent: false,
        email24hSent: false,
        notification24hSent: false,
      }));

    // Send 48-hour email reminder if not sent already
    if (user.email && !reminderRecord.emailSent) {
      await this.send48HourEmailReminder(user, job.title);
      reminderRecord.emailSent = true;
      await reminderRecord.save();
      console.log(
        `48h email reminder sent to user ${user.id} for job ${job.id}`,
      );
    }

    // Send 48-hour in-app notification if not sent already
    if (
      user.notificationPreference?.inAppNotifications &&
      !reminderRecord.notificationSent
    ) {
      await this.sendInAppReminder(user.id, job.id, '48h');
      reminderRecord.notificationSent = true;
      await reminderRecord.save();
      console.log(
        `48h in-app reminder sent to user ${user.id} for job ${job.id}`,
      );
    }
  }

  /**
   * Cron job to send 24-hour reminders
   * Runs every minute (for testing)
   */
  @Cron('* * * * *')
  async send24HourReminders() {
    console.log('Executing 24-hour job review reminder cron job');

    const completedJobs = await this.findJobsCompletedAfter24Hours();
    console.log('Found jobs completed 24 hours ago:', completedJobs.length);

    let remindersProcessed = 0;
    let reviewsAlreadySubmitted = 0;

    for (const job of completedJobs) {
      console.log(
        `Processing 24h reminder for job ${job.id} created by user ${job.createdBy.id}`,
      );

      // Check if job owner has submitted a review
      const hasOwnerReview = this.hasUserSubmittedReview(job, job.createdBy.id);
      console.log(
        `Job ${job.id}: User ${job.createdBy.id} has review: ${hasOwnerReview}`,
      );

      // If job owner hasn't submitted a review, send 24-hour reminder
      if (!hasOwnerReview) {
        await this.send24HourReminder(job, job.createdBy);
        remindersProcessed++;
      } else {
        reviewsAlreadySubmitted++;
        console.log(
          `Skipping job ${job.id} - review already submitted by user ${job.createdBy.id}`,
        );
      }
    }

    console.log(
      `24-hour reminder cron completed. Processed: ${remindersProcessed}, Already reviewed: ${reviewsAlreadySubmitted}`,
    );
  }

  /**
   * Cron job to send 48-hour reminders
   * Runs every minute (for testing)
   */
  @Cron('* * * * *')
  async send48HourReminders() {
    console.log('Executing 48-hour job review reminder cron job');

    const completedJobs = await this.findJobsCompletedAfter48Hours();
    console.log('Found jobs completed 48 hours ago:', completedJobs.length);

    let remindersProcessed = 0;
    let reviewsAlreadySubmitted = 0;

    for (const job of completedJobs) {
      console.log(
        `Processing 48h reminder for job ${job.id} created by user ${job.createdBy.id}`,
      );

      // Check if job owner has submitted a review
      const hasOwnerReview = this.hasUserSubmittedReview(job, job.createdBy.id);
      console.log(
        `Job ${job.id}: User ${job.createdBy.id} has review: ${hasOwnerReview}`,
      );

      // If job owner hasn't submitted a review, send 48-hour reminder
      if (!hasOwnerReview) {
        await this.send48HourReminder(job, job.createdBy);
        remindersProcessed++;
      } else {
        reviewsAlreadySubmitted++;
        console.log(
          `Skipping job ${job.id} - review already submitted by user ${job.createdBy.id}`,
        );
      }
    }

    console.log(
      `48-hour reminder cron completed. Processed: ${remindersProcessed}, Already reviewed: ${reviewsAlreadySubmitted}`,
    );
  }
}
