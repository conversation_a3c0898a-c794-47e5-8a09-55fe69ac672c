import {
  Column,
  <PERSON>,
  Table,
  ForeignKey,
  BelongsTo,
  DataType,
  CreatedAt,
  UpdatedAt,
} from 'sequelize-typescript';
import { User } from 'src/modules/admin/authentication/schemas/user.schema';
import { Job } from 'src/modules/job/schemas/job.schema';

@Table
export class ReviewReminder extends Model<ReviewReminder> {
  @ForeignKey(() => Job)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  jobId: number;

  @BelongsTo(() => Job)
  job: Job;

  @ForeignKey(() => User)
  @Column({
    type: DataType.INTEGER,
    allowNull: false,
  })
  userId: number;

  @BelongsTo(() => User)
  user: User;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  emailSent: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  notificationSent: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  email24hSent: boolean;

  @Column({
    type: DataType.BOOLEAN,
    defaultValue: false,
  })
  notification24hSent: boolean;

  @CreatedAt
  createdAt: Date;

  @UpdatedAt
  updatedAt: Date;
}
