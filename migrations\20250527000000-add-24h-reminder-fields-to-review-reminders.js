'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    /**
     * Add email24hSent and notification24hSent columns to ReviewReminders table
     * These flags track whether 24-hour reminders have been sent to users
     */
    try {
      // Check if columns exist before adding them
      const tableInfo = await queryInterface.describeTable('ReviewReminders');

      // Add email24hSent column if it doesn't exist
      if (!tableInfo.email24hSent) {
        await queryInterface.addColumn('ReviewReminders', 'email24hSent', {
          type: Sequelize.BOOLEAN,
          allowNull: false,
          defaultValue: false,
        });
        console.log('Column email24hSent added to ReviewReminders table');
      } else {
        console.log(
          'Column email24hSent already exists in ReviewReminders table, skipping...',
        );
      }

      // Add notification24hSent column if it doesn't exist
      if (!tableInfo.notification24hSent) {
        await queryInterface.addColumn(
          'ReviewReminders',
          'notification24hSent',
          {
            type: Sequelize.BOOLEAN,
            allowNull: false,
            defaultValue: false,
          },
        );
        console.log(
          'Column notification24hSent added to ReviewReminders table',
        );
      } else {
        console.log(
          'Column notification24hSent already exists in ReviewReminders table, skipping...',
        );
      }
    } catch (error) {
      console.error('Error in migration:', error.message);
    }
  },

  async down(queryInterface) {
    /**
     * Remove the added columns from ReviewReminders table
     */
    try {
      // Check if columns exist before removing them
      const tableInfo = await queryInterface.describeTable('ReviewReminders');

      // Remove email24hSent column if it exists
      if (tableInfo.email24hSent) {
        await queryInterface.removeColumn('ReviewReminders', 'email24hSent');
        console.log('Column email24hSent removed from ReviewReminders table');
      } else {
        console.log(
          'Column email24hSent does not exist in ReviewReminders table, skipping...',
        );
      }

      // Remove notification24hSent column if it exists
      if (tableInfo.notification24hSent) {
        await queryInterface.removeColumn(
          'ReviewReminders',
          'notification24hSent',
        );
        console.log(
          'Column notification24hSent removed from ReviewReminders table',
        );
      } else {
        console.log(
          'Column notification24hSent does not exist in ReviewReminders table, skipping...',
        );
      }
    } catch (error) {
      console.error('Error in migration rollback:', error.message);
    }
  },
};
