import {
  BadRequestException,
  HttpStatus,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { User } from '../admin/authentication/schemas/user.schema';
import { CreateReplyDto, UpdateReviewRatingDto } from './dto/review-rating.dto';
import { ReviewRating } from './schemas/review-rating.schema';
import { ReviewReminder } from './schemas/review-reminder.schema';
import { ConnectionService } from '../connection/connection.service';
import { Role } from '../roles/schemas/role.schema';
import { Op } from 'sequelize';
import { ReviewRatingStatus } from './utils/enum';
import { ERROR_MESSAGES, notificationType } from 'src/common/messages';
import { NotificationService } from '../notification/notification.service';
import { I18nContext, I18nService } from 'nestjs-i18n';
import { CustomException } from 'src/common/custom.exception';
import { SendgridService } from '../sendgrid/sendgrid.service';
import { Email } from 'src/common/email-template';
import { Cron, CronExpression } from '@nestjs/schedule';
import { NotificationPreference } from '../user-management/schemas/notification-preferences.schema';
import { Job } from '../job/schemas/job.schema';

@Injectable()
export class ReviewRatingService {
  constructor(
    @InjectModel(ReviewRating)
    private reviewRatingModel: typeof ReviewRating,
    @InjectModel(ReviewReminder)
    private reviewReminderModel: typeof ReviewReminder,
    @InjectModel(User)
    private userModel: typeof User,
    @InjectModel(Job)
    private jobModel: typeof Job,
    private readonly notificationService: NotificationService,
    private readonly connectionService: ConnectionService,
    private readonly i18n: I18nService,
    private readonly sendgridService: SendgridService,
  ) {}

  @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT) // Runs daily at midnight
  async findThoseReviewersWhoDidntReplyYet() {
    const cutoffDate = new Date(Date.now() - 48 * 60 * 60 * 1000); // 48 hours ago

    const rows = await this.reviewRatingModel.findAll({
      where: {
        parentId: null,
        status: 'active',
        createdAt: {
          [Op.lt]: cutoffDate, // created before 48 hours ago
        },
      },
    });

    for (const reviewRow of rows) {
      const matchingRows = await this.reviewRatingModel.findAll({
        where: {
          reviewerId: reviewRow.revieweeId,
          revieweeId: reviewRow.reviewerId,
        },
      });

      if (matchingRows.length === 0) {
        const userDetails = await this.userModel.findByPk(reviewRow.revieweeId);
        if (userDetails?.email) {
          // Check if we've already sent a reminder for this review
          const existingReminder = await this.reviewReminderModel.findOne({
            where: {
              userId: reviewRow.revieweeId,
              // Use the review ID instead of job ID since this is for review responses
              // We're repurposing the jobId field to store the reviewId
              jobId: reviewRow.id,
            },
          });

          // Only send the email if no reminder exists or email hasn't been sent yet
          if (!existingReminder || !existingReminder.emailSent) {
            // Create or update reminder record
            const reminderRecord =
              existingReminder ||
              (await this.reviewReminderModel.create({
                jobId: reviewRow.id, // Using jobId field to store reviewId
                userId: reviewRow.revieweeId,
                emailSent: false,
                notificationSent: false,
              }));

            // Send the email
            await this.sendgridService.sendEmail(
              userDetails.email,
              'Request for Response to Review',
              Email.getTemplate({
                title: 'Request for Response to Review',
                userName: `${userDetails?.firstName} ${userDetails?.lastName}`,
                children: `
                <p>
                  We encourage you to respond to the review you received. Your engagement matters!
                </p>
              `,
              }),
            );

            // Update the flag to indicate email was sent
            reminderRecord.emailSent = true;
            await reminderRecord.save();
          }
        }
      }
    }
  }

  // /**
  //  * Find jobs that were completed at least 48 hours ago
  //  * @returns Array of completed jobs with related data
  //  */
  // private async findJobsCompletedBefore48Hours() {
  //   // Calculate the cutoff date (48 hours ago)
  //   const twoDaysAgo = new Date(Date.now() - 48 * 60 * 60 * 1000);
  //   console.log('Looking for jobs closed before:', twoDaysAgo);

  //   // Find completed jobs that were closed at least 48 hours ago
  //   return this.jobModel.findAll({
  //     where: {
  //       status: JobStatus.CLOSED,
  //       updatedAt: {
  //         [Op.lt]: twoDaysAgo, // Jobs closed at least 48 hours ago
  //       },
  //     },
  //     include: [
  //       {
  //         model: User,
  //         as: 'createdBy',
  //         required: false,
  //         attributes: ['id', 'firstName', 'lastName', 'email'],
  //         include: [
  //           {
  //             model: NotificationPreference,
  //             as: 'notificationPreference',
  //             required: false,
  //           },
  //         ],
  //       },
  //       {
  //         model: ReviewRating,
  //         as: 'reviewRating',
  //         required: false,
  //         where: {
  //           status: 'active', // Only include active reviews
  //         },
  //       },
  //     ],
  //   });
  // }

  // /**
  //  * Check if a user has submitted a review for a job
  //  * @param job The job to check
  //  * @param userId The user ID to check
  //  * @returns Boolean indicating if the user has submitted a review
  //  */
  // private hasUserSubmittedReview(job: any, userId: number): boolean {
  //   return (
  //     Array.isArray(job.reviewRating) &&
  //     job.reviewRating.some(
  //       (review: any) =>
  //         review.reviewerId === userId &&
  //         review.jobId === job.id &&
  //         review.status === 'active',
  //     )
  //   );
  // }

  // /**
  //  * Send review reminder to a user if not already sent
  //  * @param job The job to send reminder for
  //  * @param user The user to send reminder to
  //  */
  // private async sendReminderToUser(job: any, user: any): Promise<void> {
  //   // Double-check: Verify the user hasn't submitted a review for this specific job
  //   const existingReview = await this.reviewRatingModel.findOne({
  //     where: {
  //       reviewerId: user.id,
  //       jobId: job.id,
  //       status: 'active',
  //     },
  //   });

  //   // If a review already exists, don't send reminder
  //   if (existingReview) {
  //     console.log(
  //       `Review already exists for user ${user.id} and job ${job.id}, skipping reminder`,
  //     );
  //     return;
  //   }

  //   // Check if we've already sent reminders to this user for this job
  //   const existingReminder = await this.reviewReminderModel.findOne({
  //     where: {
  //       jobId: job.id,
  //       userId: user.id,
  //     },
  //   });

  //   // If no reminder exists or notifications haven't been sent yet
  //   if (
  //     !existingReminder ||
  //     (!existingReminder.emailSent && !existingReminder.notificationSent)
  //   ) {
  //     // Create or update reminder record
  //     const reminderRecord =
  //       existingReminder ||
  //       (await this.reviewReminderModel.create({
  //         jobId: job.id,
  //         userId: user.id,
  //         emailSent: false,
  //         notificationSent: false,
  //       }));

  //     // Send email reminder if not sent already
  //     if (user.email && !reminderRecord.emailSent) {
  //       await this.sendEmailReminder(user, job.title);

  //       // Update the flag to indicate email was sent
  //       reminderRecord.emailSent = true;
  //       await reminderRecord.save();
  //       console.log(`Email reminder sent to user ${user.id} for job ${job.id}`);
  //     }

  //     // Send in-app notification if not sent already
  //     if (
  //       user.notificationPreference?.inAppNotifications &&
  //       !reminderRecord.notificationSent
  //     ) {
  //       await this.sendInAppReminder(user.id, job.id);

  //       // Update the flag to indicate notification was sent
  //       reminderRecord.notificationSent = true;
  //       await reminderRecord.save();
  //       console.log(
  //         `In-app reminder sent to user ${user.id} for job ${job.id}`,
  //       );
  //     }
  //   } else {
  //     console.log(
  //       `Reminders already sent to user ${user.id} for job ${job.id}`,
  //     );
  //   }
  // }

  // /**
  //  * Send email reminder to a user
  //  * @param user The user to send email to
  //  * @param jobTitle The title of the job
  //  */
  // private async sendEmailReminder(user: any, jobTitle: string): Promise<void> {
  //   await this.sendgridService.sendEmail(
  //     user.email,
  //     "Don't Forget to Review!",
  //     Email.getTemplate({
  //       title: "Don't Forget to Review!",
  //       userName: `${user?.firstName} ${user?.lastName}`,
  //       children: `
  //         <p>
  //           Please take a moment to review your experience with the job "${jobTitle}".
  //           Your feedback helps our community!
  //         </p>
  //       `,
  //     }),
  //   );
  // }

  // /**
  //  * Send in-app notification reminder to a user
  //  * @param userId The ID of the user to send notification to
  //  * @param jobId The ID of the job
  //  */
  // private async sendInAppReminder(
  //   userId: number,
  //   jobId: number,
  // ): Promise<void> {
  //   const data = {
  //     type: notificationType.REVIEW_REMINDER,
  //     receiverId: userId,
  //     prefrence: 'inAppNotifications',
  //     additional: {
  //       jobId: jobId,
  //     },
  //   };
  //   await this.notificationService.sendNotification(data);
  // }

  // @Cron(CronExpression.EVERY_DAY_AT_MIDNIGHT)
  // async sendJobReviewReminders() {
  //   console.log('Executing job review reminder cron job');

  //   // Check if there are any jobs with status CLOSED
  //   const closedJobsCount = await this.jobModel.count({
  //     where: {
  //       status: JobStatus.CLOSED,
  //     },
  //   });
  //   console.log('Number of jobs with status CLOSED:', closedJobsCount);

  //   // Find completed jobs that were closed at least 48 hours ago
  //   const completedJobs = await this.findJobsCompletedBefore48Hours();
  //   console.log('Found completed jobs:', completedJobs.length);

  //   let remindersProcessed = 0;
  //   let reviewsAlreadySubmitted = 0;

  //   // Process each job
  //   for (const job of completedJobs) {
  //     console.log(
  //       `Processing job ${job.id} created by user ${job.createdBy.id}`,
  //     );

  //     // Check if job owner has submitted a review
  //     const hasOwnerReview = this.hasUserSubmittedReview(job, job.createdBy.id);
  //     console.log(
  //       `Job ${job.id}: User ${job.createdBy.id} has review: ${hasOwnerReview}`,
  //     );

  //     // If job owner hasn't submitted a review, send reminder
  //     if (!hasOwnerReview) {
  //       await this.sendReminderToUser(job, job.createdBy);
  //       remindersProcessed++;
  //     } else {
  //       reviewsAlreadySubmitted++;
  //       console.log(
  //         `Skipping job ${job.id} - review already submitted by user ${job.createdBy.id}`,
  //       );
  //     }
  //   }

  //   console.log(
  //     `Job review reminder cron completed. Processed: ${remindersProcessed}, Already reviewed: ${reviewsAlreadySubmitted}`,
  //   );
  // }

  async create(createReviewRatingDto: any): Promise<ReviewRating> {
    const query = {
      where: {
        [Op.or]: [
          {
            requester_id: createReviewRatingDto.reviewerId,
            receiver_id: createReviewRatingDto.revieweeId,
          },
          {
            requester_id: createReviewRatingDto.revieweeId,
            receiver_id: createReviewRatingDto.reviewerId,
          },
        ],
      },
      attributes: ['id', 'isVerified'],
    };
    const getConnection = await this.connectionService.getOneByQuery(query);
    if (!getConnection) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Connection' },
      });
      throw new NotFoundException(message);
    }
    if (getConnection.isVerified) {
      createReviewRatingDto.isVerified = true;
    }
    const whereCondition: {
      reviewerId: number;
      revieweeId: number;
      jobId?: number;
      quotationId?: number;
    } = {
      reviewerId: createReviewRatingDto.reviewerId,
      revieweeId: createReviewRatingDto.revieweeId,
    };

    if (createReviewRatingDto.jobId) {
      whereCondition.jobId = createReviewRatingDto.jobId;
    }

    if (createReviewRatingDto.quotationId) {
      whereCondition.quotationId = createReviewRatingDto.quotationId;
    }

    const getReviewRating = await this.reviewRatingModel.findOne({
      where: whereCondition,
    });

    if (getReviewRating) {
      createReviewRatingDto.revieweRatingId = getReviewRating.id;
    }
    if (createReviewRatingDto.revieweRatingId) {
      // console.log('createReviewRatingDto....', createReviewRatingDto);

      return this.update(
        createReviewRatingDto.revieweRatingId,
        createReviewRatingDto,
      );
    }

    const reviewRating = await this.reviewRatingModel.create({
      ...createReviewRatingDto,
      status: ReviewRatingStatus.ACTIVE,
    });

    // Clean up any existing reminder records for this job and user
    // This ensures no more reminders are sent after a review is submitted
    if (createReviewRatingDto.jobId) {
      await this.reviewReminderModel.destroy({
        where: {
          jobId: createReviewRatingDto.jobId,
          userId: createReviewRatingDto.reviewerId,
        },
      });
      console.log(
        `Cleaned up reminder records for user ${createReviewRatingDto.reviewerId} and job ${createReviewRatingDto.jobId}`,
      );
    }

    const user = await this.userModel.findOne({
      where: {
        id: createReviewRatingDto.reviewerId,
      },
      include: [
        {
          model: Role,
          attributes: ['roleName'],
        },
      ],
    });
    const userProductOwner = await this.userModel.findOne({
      where: {
        id: createReviewRatingDto.revieweeId,
      },
      include: [
        {
          model: Role,
          attributes: ['roleName'],
        },
      ],
    });

    // console.log("user.email....", user.email)
    // if (user.email) {
    //   await this.sendgridService.sendEmail(
    //     user.email,
    //     'Response Confirmation',
    //     Email.getTemplate({
    //       title: 'Response Submitted',
    //       userName: `${user?.firstName} ${user?.lastName}`,
    //       children: `
    //     <p>
    //      Your response has been successfully submitted. Thank you for your feedback
    //   </p>
    //     `,
    //     }),
    //   );
    // }

    if (userProductOwner.email) {
      await this.sendgridService.sendEmail(
        userProductOwner.email,
        'Review Received Notification',
        Email.getTemplate({
          title: 'Review Received',
          userName: `${userProductOwner?.firstName} ${userProductOwner?.lastName}`,
          children: `
            <p>
             A new review has been added to your profile. Log in to see what they said.
          </p>
            `,
        }),
      );
    }
    const data = {
      type: notificationType.REVIEW_RECIEVED,
      receiverId: reviewRating.revieweeId,
      senderId: reviewRating.reviewerId,
      prefrence: 'inAppNotifications',
      additional: {
        revieweeId: reviewRating.revieweeId,
        reviewRatingId: reviewRating?.id,
        firstName: user.firstName,
        lastName: user.lastName,
        profile: user.profilePicture,
        role: user.roles[0].roleName,
      },
    };
    await this.notificationService.sendNotification(data);

    return reviewRating;
  }

  async findAndCountAll(query: any): Promise<{
    reviewRatings: ReviewRating[];
    total: number;
  }> {
    const { rows, count } = await this.reviewRatingModel.findAndCountAll(query);

    return {
      reviewRatings: rows,
      total: count,
    };
  }

  async findOne(id: number): Promise<ReviewRating> {
    const reviewRating = await this.reviewRatingModel.findOne({
      where: { id },
      include: [
        {
          model: User,
          as: 'reviewer',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          include: [
            {
              model: Role,
              attributes: ['roleName'],
            },
          ],
        },
        {
          model: User,
          as: 'reviewee',
          attributes: ['id', 'firstName', 'lastName', 'email'],
          include: [
            {
              model: Role,
              attributes: ['roleName'],
            },
          ],
        },
        { model: ReviewRating, as: 'reply', attributes: ['id', 'review'] },
      ],
    });

    if (!reviewRating) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Review rating' },
      });
      throw new NotFoundException(message);
    }

    return reviewRating;
  }

  async update(
    id: number,
    updateReviewRatingDto: UpdateReviewRatingDto,
  ): Promise<ReviewRating> {
    const reviewRating = await this.findOne(id);
    const updatedReview = await reviewRating.update(updateReviewRatingDto);

    // Clean up any existing reminder records for this job and user
    // This ensures no more reminders are sent after a review is updated
    if (updatedReview.jobId) {
      await this.reviewReminderModel.destroy({
        where: {
          jobId: updatedReview.jobId,
          userId: updatedReview.reviewerId,
        },
      });
      console.log(
        `Cleaned up reminder records for user ${updatedReview.reviewerId} and job ${updatedReview.jobId} (update)`,
      );
    }

    return updatedReview;
  }

  async remove(id: number): Promise<void> {
    const reviewRating = await this.findOne(id);
    await reviewRating.destroy();
  }

  async verifyReview(id: number, userId: number): Promise<ReviewRating> {
    const reviewRating = await this.findOne(id);

    // Check if the user has permission to verify this review
    if (
      userId !== reviewRating.reviewerId &&
      userId !== reviewRating.revieweeId
    ) {
      throw new CustomException(
        ERROR_MESSAGES.NOT_AUTHORIZED,
        HttpStatus.UNAUTHORIZED,
      );
    }

    const connection = await this.connectionService.getOneByQuery({
      where: {
        [Op.or]: [
          {
            requester_id: reviewRating.reviewerId,
            receiver_id: reviewRating.revieweeId,
          },
          {
            requester_id: reviewRating.revieweeId,
            receiver_id: reviewRating.reviewerId,
          },
        ],
      },
    });
    if (!connection.isVerified) {
      await this.connectionService.verifyConnection(
        connection.id,
        reviewRating.revieweeId,
      );
    }

    return reviewRating.update({ isVerified: true });
  }

  async createReply(
    reviewId: number,
    user: any,
    createReplyDto: CreateReplyDto,
  ): Promise<ReviewRating> {
    const originalReview = await this.reviewRatingModel.findOne({
      where: {
        id: reviewId,
      },
    });

    if (!originalReview) {
      const message = this.i18n.t('test.errorMessage.NOT_FOUND', {
        lang: I18nContext.current().lang,
        args: { data: 'Review rating' },
      });
      throw new NotFoundException(message);
    }
    if (user.id !== originalReview.revieweeId) {
      throw new CustomException(
        ERROR_MESSAGES.NOT_AUTHORIZED,
        HttpStatus.UNAUTHORIZED,
      );
    }

    const existingReply = await this.reviewRatingModel.findOne({
      where: {
        parentId: reviewId,
      },
    });
    // console.log('existingReply::::::::::', existingReply);
    if (existingReply) {
      const message = this.i18n.t('test.errorMessage.ALREADY_EXIST', {
        lang: I18nContext.current().lang,
        args: { data: 'Reply' },
      });
      throw new BadRequestException(message);
      // throw new BadRequestException(`Review already has a reply`);
    }

    const reviewReply = await this.reviewRatingModel.create({
      ...createReplyDto,
      parentId: reviewId,
      reviewerId: originalReview.revieweeId,
      revieweeId: originalReview.reviewerId,
      status: ReviewRatingStatus.ACTIVE,
    });
    if (reviewReply) {
      const data = {
        type: notificationType.RESPONSE_SUBMITTED,
        receiverId: reviewReply.revieweeId,
        senderId: reviewReply.reviewerId,
        prefrence: 'inAppNotifications',
        additional: {
          reviewId: reviewId,
          revieweeId: reviewReply.reviewerId,
          firstName: user.firstName,
          lastName: user.lastName,
          profile: user.profilePicture,
          role: user.roles[0].roleName,
          review: originalReview.review,
          reply: createReplyDto.review,
        },
      };
      await this.notificationService.sendNotification(data);
    }
    return reviewReply;
  }

  async inviteForReviewRating(
    input: any,
    user: any,
  ): Promise<{ message: string }> {
    const User = await this.userModel.findOne({
      where: {
        id: input.userId,
      },
      include: [
        {
          model: Role,
          as: 'roles',
          through: { attributes: [] },
          attributes: ['roleName'],
          required: false,
        },
        {
          model: NotificationPreference,
          as: 'notificationPreference',
        },
      ],
    });
    if (User.notificationPreference?.inAppNotifications) {
      const additional: any = {
        profile: user.profilePicture,
        firstName: user.firstName,
        lastName: user.lastName,
        role: user.roles?.[0]?.roleName || null,
        revieweeId: user.id,
      };

      if (input.jobId) {
        additional.jobId = input.jobId;
      }

      if (input.quotationId) {
        additional.quotationId = input.quotationId;
      }
      const data = {
        type: notificationType.REVIEW_REQUESTED,
        receiverId: input.userId,
        additional,
        prefrence: 'inAppNotifications',
      };
      await this.notificationService.sendNotification(data);
    }
    return { message: 'Review requested successfully' };
  }
}
